// OpenAI Service for MiCA Therapy Simulation
import OpenAI from 'openai';
import { OpenAIRequest, AgentResponse } from '../types/index.js';
import {
  openaiLogger,
  logFunctionEntry,
  logFunctionSuccess,
  logFunctionError,
  logOpenAIUsage,
  logOpenAIResponse,
  logOpenAIRequest,
} from '../utils/logger.js';

export class OpenAIService {
  private client: OpenAI;
  private defaultModel: string;
  private defaultTemperature: number;
  private defaultMaxTokens: number;

  constructor() {
    logFunctionEntry(openaiLogger, 'constructor');

    const apiKey = process.env['OPENAI_API_KEY'];
    if (!apiKey) {
      const error = new Error('OPENAI_API_KEY environment variable is required');
      logFunctionError(openaiLogger, 'constructor', error);
      throw error;
    }

    this.client = new OpenAI({
      apiKey: apiKey,
    });

    this.defaultModel = process.env['OPENAI_MODEL'] || 'gpt-4o-mini';
    this.defaultTemperature = parseFloat(process.env['OPENAI_TEMPERATURE'] || '0.7');
    this.defaultMaxTokens = parseInt(process.env['OPENAI_MAX_TOKENS'] || '500');

    const initConfig = {
      model: this.defaultModel,
      temperature: this.defaultTemperature,
      maxTokens: this.defaultMaxTokens,
    };

    logFunctionSuccess(openaiLogger, 'constructor', initConfig);
    openaiLogger.info(`OpenAI Service initialized successfully`, {
      operation: 'initialization',
      config: initConfig,
    });
  }

  /**
   * Generate a response from OpenAI
   */
  async generateResponse(request: OpenAIRequest): Promise<AgentResponse> {
    const startTime = Date.now();
    const operation = 'generateResponse';

    // Log function entry with sanitized parameters
    logFunctionEntry(openaiLogger, operation, {
      messagesCount: request.messages.length,
      temperature: request.temperature || this.defaultTemperature,
      maxTokens: request.max_tokens || this.defaultMaxTokens,
      model: this.defaultModel,
    });

    try {
      openaiLogger.info('Starting OpenAI chat completion request', {
        operation,
        phase: 'api-call',
        model: this.defaultModel,
        messagesCount: request.messages.length,
        temperature: request.temperature || this.defaultTemperature,
        maxTokens: request.max_tokens || this.defaultMaxTokens,
      });

      const completion = await this.client.chat.completions.create({
        model: this.defaultModel,
        messages: request.messages,
        temperature: request.temperature || this.defaultTemperature,
        max_completion_tokens: request.max_tokens || this.defaultMaxTokens,
      });

      const processingTime = Date.now() - startTime;
      const responseContent = completion.choices[0]?.message?.content || '';

      // Log API usage and response details
      logOpenAIRequest(openaiLogger, operation, request);
      logOpenAIUsage(openaiLogger, operation, completion.usage, processingTime);
      logOpenAIResponse(openaiLogger, operation, completion);

      const result: AgentResponse = {
        message: responseContent,
        thinking: '', // Will be populated by agent services
        metadata: {
          confidence: this.calculateConfidence(completion),
          processingTime,
          sentiment: 'neutral' as const, // Will be analyzed by agent services
          motivationLevel: 'medium' as const, // Will be analyzed by agent services
          engagementLevel: 'medium' as const, // Will be analyzed by agent services
        }
      };

      logFunctionSuccess(openaiLogger, operation, {
        responseLength: responseContent.length,
        confidence: result.metadata.confidence,
        totalTokens: completion.usage?.total_tokens,
      }, processingTime);

      return result;

    } catch (error) {
      const processingTime = Date.now() - startTime;

      // Log the error with details
      logFunctionError(openaiLogger, operation, error, processingTime);

      // Handle different types of errors with specific logging
      let errorMessage = 'I apologize, but I\'m having trouble processing your message right now. Could you please try again?';

      if (error instanceof Error) {
        if (error.message.includes('rate limit')) {
          errorMessage = 'OpenAI rate limit exceeded. Please try again later.';
          openaiLogger.warn('OpenAI rate limit exceeded', {
            operation,
            phase: 'rate-limit',
            processingTime: `${processingTime}ms`,
          });
          throw new Error(errorMessage);
        } else if (error.message.includes('insufficient_quota')) {
          errorMessage = 'OpenAI quota exceeded. Please check your billing.';
          openaiLogger.error('OpenAI quota exceeded', {
            operation,
            phase: 'quota-exceeded',
            processingTime: `${processingTime}ms`,
          });
          throw new Error(errorMessage);
        } else if (error.message.includes('invalid_api_key')) {
          errorMessage = 'Invalid OpenAI API key. Please check your configuration.';
          openaiLogger.error('Invalid OpenAI API key', {
            operation,
            phase: 'auth-error',
            processingTime: `${processingTime}ms`,
          });
          throw new Error(errorMessage);
        }
      }

      // Log fallback response
      openaiLogger.info('Returning fallback response due to error', {
        operation,
        phase: 'fallback',
        processingTime: `${processingTime}ms`,
      });

      return {
        message: errorMessage,
        thinking: 'Error occurred while generating response',
        metadata: {
          confidence: 0,
          processingTime,
          sentiment: 'neutral' as const,
          motivationLevel: 'medium' as const,
          engagementLevel: 'medium' as const,
        }
      };
    }
  }

  /**
   * Generate a thinking process for an agent
   */
  async generateThinking(
    agentType: 'therapist' | 'patient',
    context: string,
    prompt: string
  ): Promise<string> {
    const startTime = Date.now();
    const operation = 'generateThinking';

    logFunctionEntry(openaiLogger, operation, {
      agentType,
      contextLength: context.length,
      promptLength: prompt.length,
    });

    try {
      openaiLogger.info('Starting thinking generation request', {
        operation,
        phase: 'api-call',
        agentType,
        model: this.defaultModel,
        temperature: 0.8,
        maxTokens: 200,
      });

      const completion = await this.client.chat.completions.create({
        model: this.defaultModel,
        messages: [
          {
            role: 'system',
            content: `You are generating internal thoughts for a ${agentType} in a therapy simulation. Provide realistic internal thoughts, considerations, and reasoning processes.`
          },
          {
            role: 'user',
            content: `Context: ${context}\n\nPrompt: ${prompt}`
          }
        ],
        temperature: 0.8,
        max_completion_tokens: 200,
      });

      const processingTime = Date.now() - startTime;
      const thinking = completion.choices[0]?.message?.content || 'Processing...';

      // Log API usage and response details
      logOpenAIUsage(openaiLogger, operation, completion.usage, processingTime);
      logOpenAIResponse(openaiLogger, operation, completion);

      logFunctionSuccess(openaiLogger, operation, {
        agentType,
        thinkingLength: thinking.length,
        totalTokens: completion.usage?.total_tokens,
      }, processingTime);

      openaiLogger.debug('Thinking content generated', {
        operation,
        phase: 'content',
        agentType,
        thinkingPreview: thinking.substring(0, 100) + (thinking.length > 100 ? '...' : ''),
      });

      return thinking;

    } catch (error) {
      const processingTime = Date.now() - startTime;
      const fallbackThinking = agentType === 'therapist'
        ? 'Analyzing patient response and considering therapeutic approach...'
        : 'Processing therapist\'s words and formulating response...';

      logFunctionError(openaiLogger, operation, error, processingTime);

      openaiLogger.info('Returning fallback thinking due to error', {
        operation,
        phase: 'fallback',
        agentType,
        processingTime: `${processingTime}ms`,
      });

      return fallbackThinking;
    }
  }

  /**
   * Analyze sentiment of a message
   */
  async analyzeSentiment(message: string): Promise<'positive' | 'negative' | 'neutral'> {
    const startTime = Date.now();
    const operation = 'analyzeSentiment';

    logFunctionEntry(openaiLogger, operation, {
      messageLength: message.length,
    });

    try {
      openaiLogger.info('Starting sentiment analysis request', {
        operation,
        phase: 'api-call',
        model: this.defaultModel,
        temperature: 0.1,
        maxTokens: 10,
      });

      const completion = await this.client.chat.completions.create({
        model: this.defaultModel,
        messages: [
          {
            role: 'system',
            content: 'Analyze the sentiment of the following message. Respond with only one word: positive, negative, or neutral.'
          },
          {
            role: 'user',
            content: message
          }
        ],
        temperature: 0.1,
        max_completion_tokens: 10,
      });

      const processingTime = Date.now() - startTime;
      const sentiment = completion.choices[0]?.message?.content?.toLowerCase().trim();

      // Log API usage
      logOpenAIUsage(openaiLogger, operation, completion.usage, processingTime);

      if (sentiment === 'positive' || sentiment === 'negative' || sentiment === 'neutral') {
        logFunctionSuccess(openaiLogger, operation, {
          sentiment,
          totalTokens: completion.usage?.total_tokens,
        }, processingTime);

        return sentiment;
      }

      // Invalid sentiment response, log warning and return default
      openaiLogger.warn('Invalid sentiment response from OpenAI', {
        operation,
        phase: 'invalid-response',
        receivedSentiment: sentiment,
        processingTime: `${processingTime}ms`,
      });

      return 'neutral';

    } catch (error) {
      const processingTime = Date.now() - startTime;
      logFunctionError(openaiLogger, operation, error, processingTime);

      openaiLogger.info('Returning default sentiment due to error', {
        operation,
        phase: 'fallback',
        defaultSentiment: 'neutral',
        processingTime: `${processingTime}ms`,
      });

      return 'neutral';
    }
  }

  /**
   * Analyze engagement level
   */
  async analyzeEngagement(message: string): Promise<'low' | 'medium' | 'high'> {
    const startTime = Date.now();
    const operation = 'analyzeEngagement';

    logFunctionEntry(openaiLogger, operation, {
      messageLength: message.length,
    });

    try {
      openaiLogger.info('Starting engagement analysis request', {
        operation,
        phase: 'api-call',
        model: this.defaultModel,
        temperature: 0.1,
        maxTokens: 10,
      });

      const completion = await this.client.chat.completions.create({
        model: this.defaultModel,
        messages: [
          {
            role: 'system',
            content: 'Analyze the engagement level of this message in a therapy context. Consider factors like detail, emotional expression, and willingness to share. Respond with only one word: low, medium, or high.'
          },
          {
            role: 'user',
            content: message
          }
        ],
        temperature: 0.1,
        max_completion_tokens: 10,
      });

      const processingTime = Date.now() - startTime;
      const engagement = completion.choices[0]?.message?.content?.toLowerCase().trim();

      // Log API usage
      logOpenAIUsage(openaiLogger, operation, completion.usage, processingTime);

      if (engagement === 'low' || engagement === 'medium' || engagement === 'high') {
        logFunctionSuccess(openaiLogger, operation, {
          engagement,
          totalTokens: completion.usage?.total_tokens,
        }, processingTime);

        return engagement;
      }

      // Invalid engagement response, log warning and return default
      openaiLogger.warn('Invalid engagement response from OpenAI', {
        operation,
        phase: 'invalid-response',
        receivedEngagement: engagement,
        processingTime: `${processingTime}ms`,
      });

      return 'medium';

    } catch (error) {
      const processingTime = Date.now() - startTime;
      logFunctionError(openaiLogger, operation, error, processingTime);

      openaiLogger.info('Returning default engagement due to error', {
        operation,
        phase: 'fallback',
        defaultEngagement: 'medium',
        processingTime: `${processingTime}ms`,
      });

      return 'medium';
    }
  }

  /**
   * Analyze motivation level
   */
  async analyzeMotivation(message: string): Promise<'low' | 'medium' | 'high'> {
    const startTime = Date.now();
    const operation = 'analyzeMotivation';

    logFunctionEntry(openaiLogger, operation, {
      messageLength: message.length,
    });

    try {
      openaiLogger.info('Starting motivation analysis request', {
        operation,
        phase: 'api-call',
        model: this.defaultModel,
        temperature: 0.1,
        maxTokens: 10,
      });

      const completion = await this.client.chat.completions.create({
        model: this.defaultModel,
        messages: [
          {
            role: 'system',
            content: 'Analyze the motivation level for change and therapy engagement in this message. Consider willingness to work on issues, hope for improvement, and commitment to the process. Respond with only one word: low, medium, or high.'
          },
          {
            role: 'user',
            content: message
          }
        ],
        temperature: 0.1,
        max_completion_tokens: 10,
      });

      const processingTime = Date.now() - startTime;
      const motivation = completion.choices[0]?.message?.content?.toLowerCase().trim();

      // Log API usage
      logOpenAIUsage(openaiLogger, operation, completion.usage, processingTime);

      if (motivation === 'low' || motivation === 'medium' || motivation === 'high') {
        logFunctionSuccess(openaiLogger, operation, {
          motivation,
          totalTokens: completion.usage?.total_tokens,
        }, processingTime);

        return motivation;
      }

      // Invalid motivation response, log warning and return default
      openaiLogger.warn('Invalid motivation response from OpenAI', {
        operation,
        phase: 'invalid-response',
        receivedMotivation: motivation,
        processingTime: `${processingTime}ms`,
      });

      return 'medium';

    } catch (error) {
      const processingTime = Date.now() - startTime;
      logFunctionError(openaiLogger, operation, error, processingTime);

      openaiLogger.info('Returning default motivation due to error', {
        operation,
        phase: 'fallback',
        defaultMotivation: 'medium',
        processingTime: `${processingTime}ms`,
      });

      return 'medium';
    }
  }

  /**
   * Calculate confidence score based on OpenAI response
   */
  private calculateConfidence(completion: OpenAI.Chat.Completions.ChatCompletion): number {
    const operation = 'calculateConfidence';

    // Simple confidence calculation based on response characteristics
    const choice = completion.choices[0];
    if (!choice) {
      openaiLogger.debug('No choice available for confidence calculation', {
        operation,
        phase: 'no-choice',
      });
      return 0;
    }

    let confidence = 0.7; // Base confidence
    const messageLength = choice.message?.content?.length || 0;

    // Adjust based on finish reason
    if (choice.finish_reason === 'stop') {
      confidence += 0.2;
    } else if (choice.finish_reason === 'length') {
      confidence -= 0.1;
    }

    // Adjust based on response length (very short or very long responses might be less confident)
    if (messageLength > 50 && messageLength < 500) {
      confidence += 0.1;
    }

    const finalConfidence = Math.max(0, Math.min(1, confidence));

    openaiLogger.debug('Confidence calculated', {
      operation,
      phase: 'calculation',
      finishReason: choice.finish_reason,
      messageLength,
      baseConfidence: 0.7,
      finalConfidence,
    });

    return finalConfidence;
  }

  /**
   * Test the OpenAI connection
   */
  async testConnection(): Promise<boolean> {
    const startTime = Date.now();
    const operation = 'testConnection';

    logFunctionEntry(openaiLogger, operation);

    try {
      openaiLogger.info('Starting OpenAI connection test', {
        operation,
        phase: 'api-call',
        model: this.defaultModel,
        maxTokens: 5,
      });

      const completion = await this.client.chat.completions.create({
        model: this.defaultModel,
        messages: [{ role: 'user', content: 'Hello' }],
        max_completion_tokens: 5,
      });

      const processingTime = Date.now() - startTime;
      const success = !!completion.choices[0]?.message?.content;

      // Log API usage
      logOpenAIUsage(openaiLogger, operation, completion.usage, processingTime);

      if (success) {
        logFunctionSuccess(openaiLogger, operation, {
          connectionStatus: 'successful',
          totalTokens: completion.usage?.total_tokens,
        }, processingTime);
      } else {
        openaiLogger.warn('OpenAI connection test returned empty response', {
          operation,
          phase: 'empty-response',
          processingTime: `${processingTime}ms`,
        });
      }

      return success;

    } catch (error) {
      const processingTime = Date.now() - startTime;
      logFunctionError(openaiLogger, operation, error, processingTime);

      openaiLogger.error('OpenAI connection test failed', {
        operation,
        phase: 'connection-failed',
        processingTime: `${processingTime}ms`,
      });

      return false;
    }
  }
}
