// Patient Agent Service for MiCA Therapy Simulation
import { OpenAIService } from '../openai.js';
import { AgentResponse, ConversationContext } from '../../types/index.js';
import { PatientConfig, defaultConversationConfig } from '../../config/conversation.js';

export class PatientAgentService {
  private openaiService: OpenAIService;
  private config: PatientConfig;
  private conversationHistory: Array<{ role: string; content: string; timestamp: string }> = [];
  private emotionalState: PatientConfig['emotionalState'];
  private sessionProgress: {
    trustBuilding: number; // 0-100
    openness: number; // 0-100
    engagement: number; // 0-100
  };

  constructor(openaiService: OpenAIService, config?: PatientConfig) {
    this.openaiService = openaiService;
    this.config = config || defaultConversationConfig.patient;
    this.emotionalState = { ...this.config.emotionalState };
    
    this.sessionProgress = {
      trustBuilding: this.config.emotionalState.trustLevel,
      openness: this.getOpennessScore(this.config.emotionalState.openness),
      engagement: this.getEngagementScore(this.config.emotionalState.motivationLevel)
    };
    
    console.log(`👤 Patient Agent initialized: ${this.config.persona.name}`);
    console.log(`😔 Current mood: ${this.emotionalState.primaryMood}`);
    console.log(`🔒 Openness level: ${this.emotionalState.openness}`);
  }

  /**
   * Generate response to therapist message
   */
  async generateResponse(
    therapistMessage: string,
    conversationContext: ConversationContext
  ): Promise<AgentResponse> {
    console.log('👤 Patient generating response to therapist message...');
    console.log(`👩‍⚕️ Therapist said: "${therapistMessage.substring(0, 100)}..."`);

    const startTime = Date.now();

    try {
      // Update emotional state based on conversation progress
      this.updateEmotionalState(therapistMessage);

      // Build conversation context for the AI
      const systemPrompt = this.buildSystemPrompt();
      const conversationMessages = this.buildConversationMessages(therapistMessage, conversationContext);

      // Generate the main response
      const response = await this.openaiService.generateResponse({
        messages: [
          { role: 'system', content: systemPrompt },
          ...conversationMessages
        ],
        temperature: 0.8, // Higher temperature for more varied patient responses
        max_tokens: 250
      });

      // Generate thinking process
      const thinking = await this.generateThinking(
        `Therapist just said: "${therapistMessage}"`,
        'How do I feel about this? What should I share? How much can I trust this person?'
      );

      const processingTime = Date.now() - startTime;

      // Update conversation history
      this.conversationHistory.push({
        role: 'therapist',
        content: therapistMessage,
        timestamp: new Date().toISOString()
      });
      this.conversationHistory.push({
        role: 'patient',
        content: response.message,
        timestamp: new Date().toISOString()
      });

      console.log(`✅ Patient response generated in ${processingTime}ms`);
      console.log(`😊 Current emotional state: ${this.emotionalState.primaryMood} (${this.emotionalState.energyLevel} energy)`);

      return {
        message: response.message,
        thinking,
        metadata: {
          sentiment: this.mapMoodToSentiment(this.emotionalState.primaryMood),
          motivationLevel: this.emotionalState.motivationLevel,
          engagementLevel: this.getEngagementLevel(),
          confidence: response.metadata.confidence,
          processingTime
        }
      };

    } catch (error) {
      console.error('❌ Error generating patient response:', error);
      
      return {
        message: "I... I'm not sure how to put this into words right now.",
        thinking: "I'm feeling overwhelmed and having trouble expressing myself clearly.",
        metadata: {
          sentiment: 'negative',
          motivationLevel: 'low',
          engagementLevel: 'low',
          confidence: 0.3,
          processingTime: Date.now() - startTime
        }
      };
    }
  }

  /**
   * Generate thinking process for the patient
   */
  private async generateThinking(context: string, question: string): Promise<string> {
    const thinkingPrompt = `
As ${this.config.persona.name}, a ${this.config.persona.age}-year-old ${this.config.persona.background}

Your current emotional state:
- Mood: ${this.emotionalState.primaryMood}
- Energy: ${this.emotionalState.energyLevel}
- Trust level: ${this.emotionalState.trustLevel}/100
- Openness: ${this.emotionalState.openness}

Your personality traits: ${this.config.persona.personalityTraits.join(', ')}
Your concerns: ${this.config.concerns.join(', ')}

Context: ${context}

Consider your internal thoughts, hesitations, and emotional reactions. What are you really thinking and feeling?

Question: ${question}

Provide your internal thoughts and emotional processing:`;

    try {
      return await this.openaiService.generateThinking('patient', context, thinkingPrompt);
    } catch (error) {
      console.error('❌ Error generating patient thinking:', error);
      return 'I\'m trying to process what the therapist said and figure out how much I want to share...';
    }
  }

  /**
   * Build system prompt for the patient
   */
  private buildSystemPrompt(): string {
    return `${this.config.prompts.systemPrompt}

Current emotional state:
- Primary mood: ${this.emotionalState.primaryMood}
- Energy level: ${this.emotionalState.energyLevel}
- Openness: ${this.emotionalState.openness}
- Trust level: ${this.emotionalState.trustLevel}/100
- Motivation: ${this.emotionalState.motivationLevel}

Response patterns:
- Tendency: ${this.config.responsePatterns.tendency}
- Emotional expression: ${this.config.responsePatterns.emotionalExpression}
- Detail level: ${this.config.responsePatterns.detailLevel}

Session progress:
- Trust building: ${this.sessionProgress.trustBuilding}/100
- Openness: ${this.sessionProgress.openness}/100
- Engagement: ${this.sessionProgress.engagement}/100

Remember:
- You're in therapy for the first time and may feel nervous or uncertain
- Your responses should reflect your current emotional state and trust level
- Be authentic to your personality while showing gradual progress
- Don't reveal everything at once - trust builds slowly`;
  }

  /**
   * Build conversation messages for OpenAI
   */
  private buildConversationMessages(
    currentTherapistMessage: string,
    _context: ConversationContext
  ): Array<{ role: 'user' | 'assistant'; content: string }> {
    const messages: Array<{ role: 'user' | 'assistant'; content: string }> = [];

    // Add recent conversation history (last 6 messages to stay within token limits)
    const recentHistory = this.conversationHistory.slice(-6);
    
    for (const msg of recentHistory) {
      messages.push({
        role: msg.role === 'therapist' ? 'user' : 'assistant',
        content: msg.content
      });
    }

    // Add current therapist message
    messages.push({
      role: 'user',
      content: currentTherapistMessage
    });

    return messages;
  }

  /**
   * Update emotional state based on conversation progress
   */
  private updateEmotionalState(therapistMessage: string): void {
    // Simple emotional state evolution based on therapist interaction
    const isEmpathetic = therapistMessage.toLowerCase().includes('understand') ||
                        therapistMessage.toLowerCase().includes('hear you') ||
                        therapistMessage.toLowerCase().includes('feel');
    
    // Gradually increase trust if therapist is empathetic
    if (isEmpathetic && this.emotionalState.trustLevel < 80) {
      this.emotionalState.trustLevel = Math.min(100, this.emotionalState.trustLevel + 5);
      this.sessionProgress.trustBuilding = this.emotionalState.trustLevel;
    }

    // Adjust openness based on trust level
    if (this.emotionalState.trustLevel > 60 && this.emotionalState.openness === 'guarded') {
      this.emotionalState.openness = 'open';
      this.sessionProgress.openness = this.getOpennessScore('open');
    } else if (this.emotionalState.trustLevel > 80 && this.emotionalState.openness === 'open') {
      this.emotionalState.openness = 'very_open';
      this.sessionProgress.openness = this.getOpennessScore('very_open');
    }

    // Slight mood improvement over time with good therapeutic interaction
    if (isEmpathetic && this.conversationHistory.length > 4) {
      if (this.emotionalState.primaryMood === 'anxious' && Math.random() > 0.7) {
        this.emotionalState.primaryMood = 'neutral';
      } else if (this.emotionalState.primaryMood === 'neutral' && Math.random() > 0.8) {
        this.emotionalState.primaryMood = 'hopeful';
      }
    }

    console.log(`📊 Emotional state updated - Trust: ${this.emotionalState.trustLevel}, Openness: ${this.emotionalState.openness}, Mood: ${this.emotionalState.primaryMood}`);
  }

  /**
   * Get current engagement level
   */
  private getEngagementLevel(): 'low' | 'medium' | 'high' {
    const avgProgress = (this.sessionProgress.trustBuilding + this.sessionProgress.openness + this.sessionProgress.engagement) / 3;
    
    if (avgProgress < 40) return 'low';
    if (avgProgress < 70) return 'medium';
    return 'high';
  }

  /**
   * Map mood to sentiment
   */
  private mapMoodToSentiment(mood: PatientConfig['emotionalState']['primaryMood']): 'positive' | 'negative' | 'neutral' {
    switch (mood) {
      case 'hopeful': return 'positive';
      case 'depressed':
      case 'anxious':
      case 'frustrated':
      case 'angry': return 'negative';
      case 'neutral':
      default: return 'neutral';
    }
  }

  /**
   * Convert openness to numeric score
   */
  private getOpennessScore(openness: PatientConfig['emotionalState']['openness']): number {
    switch (openness) {
      case 'closed': return 20;
      case 'guarded': return 40;
      case 'open': return 70;
      case 'very_open': return 90;
      default: return 40;
    }
  }

  /**
   * Convert motivation to engagement score
   */
  private getEngagementScore(motivation: PatientConfig['emotionalState']['motivationLevel']): number {
    switch (motivation) {
      case 'low': return 30;
      case 'medium': return 60;
      case 'high': return 90;
      default: return 60;
    }
  }

  /**
   * Get current emotional state
   */
  getCurrentEmotionalState(): PatientConfig['emotionalState'] {
    return { ...this.emotionalState };
  }

  /**
   * Get session progress
   */
  getSessionProgress(): typeof this.sessionProgress {
    return { ...this.sessionProgress };
  }

  /**
   * Get conversation history
   */
  getConversationHistory(): Array<{ role: string; content: string; timestamp: string }> {
    return [...this.conversationHistory];
  }

  /**
   * Clear conversation history and reset state
   */
  clearHistory(): void {
    console.log('🗑️ Clearing patient conversation history and resetting state');
    this.conversationHistory = [];
    this.emotionalState = { ...this.config.emotionalState };
    this.sessionProgress = {
      trustBuilding: this.config.emotionalState.trustLevel,
      openness: this.getOpennessScore(this.config.emotionalState.openness),
      engagement: this.getEngagementScore(this.config.emotionalState.motivationLevel)
    };
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<PatientConfig>): void {
    this.config = { ...this.config, ...newConfig };
    if (newConfig.emotionalState) {
      this.emotionalState = { ...this.emotionalState, ...newConfig.emotionalState };
    }
    console.log('⚙️ Patient configuration updated');
  }
}
